
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .controls {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .chromosome-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .chromosome-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .chromosome-btn.active {
            background: #667eea;
            color: white;
        }
        .chromosome-btn:hover {
            background: #667eea;
            color: white;
        }
        .gap-visualization {
            padding: 30px;
        }
        .chromosome-section {
            margin-bottom: 40px;
            display: none;
        }
        .chromosome-section.active {
            display: block;
        }
        .chromosome-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .gap-track {
            position: relative;
            height: 60px;
            background: #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            overflow: hidden;
        }
        .gap-item {
            position: absolute;
            height: 100%;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        .gap-item:hover {
            transform: scaleY(1.2);
            z-index: 10;
        }
        .gap-filled {
            background: #28a745;
        }
        .gap-unfilled {
            background: #dc3545;
        }
        .gap-details {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        .gap-details.show {
            display: block;
        }
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .detail-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .detail-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .detail-value {
            color: #666;
        }
        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DEGAP Gap Filling Report</h1>
            <p>Interactive visualization of genome gap filling results</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">20</div>
                <div class="stat-label">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">60.0%</div>
                <div class="stat-label">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">Chromosomes</div>
            </div>
        </div>

        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Fill Type Distribution</h3>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">Unknown</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">100.0%</div>
            </div>
        </div>

        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Gap Length Distribution</h3>
            <div class="stat-card">
                <div class="stat-number">2/4</div>
                <div class="stat-label">Very Small (< 100 bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4/4</div>
                <div class="stat-label">Small (100-1K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: 100.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5/10</div>
                <div class="stat-label">Medium (1K-10K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1/2</div>
                <div class="stat-label">Large (10K-50K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0/0</div>
                <div class="stat-label">Very Large (> 50K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: 0.0%</div>
            </div>
        </div>

        <div class="controls">
            <h3>Select Chromosome:</h3>
            <div class="chromosome-selector">
                <button class="chromosome-btn" onclick="showChromosome('chr1A_TA299')">chr1A_TA299</button>
            </div>
        </div>

        <div class="gap-visualization">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span>Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span>Unfilled Gap</span>
                </div>
            </div>
            <div class="chromosome-section" id="chr-chr1A_TA299">
                <div class="chromosome-title">chr1A_TA299</div>
                <div class="gap-track" id="track-chr1A_TA299">
                    <div class="gap-item gap-unfilled"
                         style="left: 0.02%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 1)"
                         title="Gap 1: 138,402-138,424 (23 bp)">
                        1
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 0.04%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 2)"
                         title="Gap 2: 254,354-272,628 (18,275 bp)">
                        2
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 4.73%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 3)"
                         title="Gap 3: 30,054,223-30,054,322 (100 bp)">
                        3
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 6.75%; width: 0.01%;"
                         onclick="showGapDetails('chr1A_TA299', 4)"
                         title="Gap 4: 42,926,970-42,976,731 (49,762 bp)">
                        4
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 9.07%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 5)"
                         title="Gap 5: 57,693,457-57,694,440 (984 bp)">
                        5
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 11.24%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 6)"
                         title="Gap 6: 71,479,355-71,482,452 (3,098 bp)">
                        6
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 17.30%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 7)"
                         title="Gap 7: 110,023,940-110,027,276 (3,337 bp)">
                        7
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 23.64%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 8)"
                         title="Gap 8: 150,338,264-150,343,236 (4,973 bp)">
                        8
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 25.11%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 9)"
                         title="Gap 9: 159,684,345-159,685,915 (1,571 bp)">
                        9
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 28.66%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 10)"
                         title="Gap 10: 182,271,447-182,271,469 (23 bp)">
                        10
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 32.70%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 11)"
                         title="Gap 11: 207,927,380-207,929,943 (2,564 bp)">
                        11
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 45.01%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 12)"
                         title="Gap 12: 286,215,887-286,218,916 (3,030 bp)">
                        12
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 48.02%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 13)"
                         title="Gap 13: 305,356,130-305,357,762 (1,633 bp)">
                        13
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 67.93%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 14)"
                         title="Gap 14: 431,979,574-431,982,803 (3,230 bp)">
                        14
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 95.40%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 15)"
                         title="Gap 15: 606,694,072-606,694,094 (23 bp)">
                        15
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 96.45%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 16)"
                         title="Gap 16: 613,338,462-613,342,080 (3,619 bp)">
                        16
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 97.02%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 17)"
                         title="Gap 17: 617,024,912-617,025,585 (674 bp)">
                        17
                    </div>
                    <div class="gap-item gap-unfilled"
                         style="left: 97.15%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 18)"
                         title="Gap 18: 617,827,052-617,830,319 (3,268 bp)">
                        18
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 99.52%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 19)"
                         title="Gap 19: 632,889,350-632,890,236 (887 bp)">
                        19
                    </div>
                    <div class="gap-item gap-filled"
                         style="left: 100.00%; width: 0.00%;"
                         onclick="showGapDetails('chr1A_TA299', 20)"
                         title="Gap 20: 635,944,767-635,944,789 (23 bp)">
                        20
                    </div>
                </div>
                <div class="gap-details" id="details-chr1A_TA299">
                    <div class="detail-grid" id="detail-grid-chr1A_TA299">
                        <!-- Gap details will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Gap data for JavaScript
        const gapData = {
        "chr1A_TA299": {
                "1": {
                        "start": 138402,
                        "end": 138424,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 138402,
                        "new_end": 138424
                },
                "2": {
                        "start": 254354,
                        "end": 272628,
                        "gap_len": 18275,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 254354,
                        "new_end": 272628
                },
                "3": {
                        "start": 30054223,
                        "end": 30054322,
                        "gap_len": 100,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1796,
                        "fill_type": null,
                        "new_start": 30054223,
                        "new_end": 30054322
                },
                "4": {
                        "start": 42926970,
                        "end": 42976731,
                        "gap_len": 49762,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 113258,
                        "fill_type": null,
                        "new_start": 42926970,
                        "new_end": 42976731
                },
                "5": {
                        "start": 57693457,
                        "end": 57694440,
                        "gap_len": 984,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 1176,
                        "fill_type": null,
                        "new_start": 57693457,
                        "new_end": 57694440
                },
                "6": {
                        "start": 71479355,
                        "end": 71482452,
                        "gap_len": 3098,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": -1214,
                        "fill_type": null,
                        "new_start": 71479355,
                        "new_end": 71482452
                },
                "7": {
                        "start": 110023940,
                        "end": 110027276,
                        "gap_len": 3337,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 110023940,
                        "new_end": 110027276
                },
                "8": {
                        "start": 150338264,
                        "end": 150343236,
                        "gap_len": 4973,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -10543,
                        "fill_type": null,
                        "new_start": 150338264,
                        "new_end": 150343236
                },
                "9": {
                        "start": 159684345,
                        "end": 159685915,
                        "gap_len": 1571,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 1332,
                        "fill_type": null,
                        "new_start": 159684345,
                        "new_end": 159685915
                },
                "10": {
                        "start": 182271447,
                        "end": 182271469,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 182271447,
                        "new_end": 182271469
                },
                "11": {
                        "start": 207927380,
                        "end": 207929943,
                        "gap_len": 2564,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 207927380,
                        "new_end": 207929943
                },
                "12": {
                        "start": 286215887,
                        "end": 286218916,
                        "gap_len": 3030,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 3117,
                        "fill_type": null,
                        "new_start": 286215887,
                        "new_end": 286218916
                },
                "13": {
                        "start": 305356130,
                        "end": 305357762,
                        "gap_len": 1633,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 305356130,
                        "new_end": 305357762
                },
                "14": {
                        "start": 431979574,
                        "end": 431982803,
                        "gap_len": 3230,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 2635,
                        "fill_type": null,
                        "new_start": 431979574,
                        "new_end": 431982803
                },
                "15": {
                        "start": 606694072,
                        "end": 606694094,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1026,
                        "fill_type": null,
                        "new_start": 606694072,
                        "new_end": 606694094
                },
                "16": {
                        "start": 613338462,
                        "end": 613342080,
                        "gap_len": 3619,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 613338462,
                        "new_end": 613342080
                },
                "17": {
                        "start": 617024912,
                        "end": 617025585,
                        "gap_len": 674,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 723,
                        "fill_type": null,
                        "new_start": 617024912,
                        "new_end": 617025585
                },
                "18": {
                        "start": 617827052,
                        "end": 617830319,
                        "gap_len": 3268,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 617827052,
                        "new_end": 617830319
                },
                "19": {
                        "start": 632889350,
                        "end": 632890236,
                        "gap_len": 887,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1263,
                        "fill_type": null,
                        "new_start": 632889350,
                        "new_end": 632890236
                },
                "20": {
                        "start": 635944767,
                        "end": 635944789,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -3802,
                        "fill_type": null,
                        "new_start": 635944767,
                        "new_end": 635944789
                }
        }
};

        function showChromosome(chromName) {
            // Hide all chromosome sections
            const sections = document.querySelectorAll('.chromosome-section');
            sections.forEach(section => section.classList.remove('active'));

            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.chromosome-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected chromosome
            const selectedSection = document.getElementById('chr-' + chromName);
            if (selectedSection) {
                selectedSection.classList.add('active');
            }

            // Activate selected button
            event.target.classList.add('active');

            // Hide gap details
            const details = document.getElementById('details-' + chromName);
            if (details) {
                details.classList.remove('show');
            }
        }

        function showGapDetails(chromName, gapId) {
            const gap = gapData[chromName][gapId];
            const detailsDiv = document.getElementById('details-' + chromName);
            const gridDiv = document.getElementById('detail-grid-' + chromName);

            if (!gap || !detailsDiv || !gridDiv) return;

            // Build details HTML
            let detailsHTML = `
                <div class="detail-item">
                    <div class="detail-label">Gap ID</div>
                    <div class="detail-value">${gapId}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Position</div>
                    <div class="detail-value">${gap.start.toLocaleString()} - ${gap.end.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Original Length</div>
                    <div class="detail-value">${gap.gap_len.toLocaleString()} bp</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Status</div>
                    <div class="detail-value" style="color: ${gap.status === 'filled' ? '#28a745' : '#dc3545'}">
                        ${gap.status === 'filled' ? 'Filled' : 'Unfilled'}
                    </div>
                </div>`;

            if (gap.status === 'filled') {
                detailsHTML += `
                    <div class="detail-item">
                        <div class="detail-label">Fill Direction</div>
                        <div class="detail-value">${gap.fill_direction || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Fill Type</div>
                        <div class="detail-value">${gap.fill_type || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Fill Length</div>
                        <div class="detail-value">${gap.fill_length !== null ? gap.fill_length.toLocaleString() + ' bp' : 'Unknown'}</div>
                    </div>`;
            }

            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">Left Flanking Length</div>
                    <div class="detail-value">${gap.left_len.toLocaleString()} bp</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Right Flanking Length</div>
                    <div class="detail-value">${gap.right_len.toLocaleString()} bp</div>
                </div>`;

            gridDiv.innerHTML = detailsHTML;
            detailsDiv.classList.add('show');
        }

        // Show first chromosome by default
        document.addEventListener('DOMContentLoaded', function() {
            const firstBtn = document.querySelector('.chromosome-btn');
            if (firstBtn) {
                firstBtn.click();
            }
        });
    </script>
</body>
</html>
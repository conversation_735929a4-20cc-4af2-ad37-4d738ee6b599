#!/usr/bin/env python3
"""
DEGAP HTML Report Generator - Final Version
Generate interactive HTML visualization for DEGAP gap filling results
"""

import os
import sys
import json
import argparse
from collections import defaultdict
import re

def parse_gap_log(gap_log_path):
    """Parse gap.log file to get original gap information"""
    gaps = {}
    if not os.path.exists(gap_log_path):
        return gaps
    
    with open(gap_log_path, 'r') as f:
        next(f)  # Skip header
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 7:
                chrom = parts[0]
                gap_id = int(parts[1])
                start = int(parts[2])
                end = int(parts[3])
                gap_len = int(parts[4])
                left_len = int(parts[5])
                right_len = int(parts[6])
                
                if chrom not in gaps:
                    gaps[chrom] = {}
                
                gaps[chrom][gap_id] = {
                    'start': start,
                    'end': end,
                    'gap_len': gap_len,
                    'left_len': left_len,
                    'right_len': right_len,
                    'status': 'unfilled',
                    'fill_direction': None,
                    'fill_length': None,
                    'fill_type': None,
                    'new_start': start,
                    'new_end': end
                }
    return gaps

def parse_detailed_process_log(detailed_log_path):
    """Parse detailed_process.log to get filling results"""
    filled_info = {}
    unfilled_info = {}
    
    if not os.path.exists(detailed_log_path):
        return filled_info, unfilled_info
    
    with open(detailed_log_path, 'r') as f:
        content = f.read()
    
    # Parse filled gaps
    filled_section = False
    unfilled_section = False
    
    for line in content.split('\n'):
        line = line.strip()
        
        if line.startswith('## Filled Gap Details'):
            filled_section = True
            unfilled_section = False
            continue
        elif line.startswith('## Unfilled Gap Information'):
            filled_section = False
            unfilled_section = True
            continue
        elif line.startswith('## '):
            filled_section = False
            unfilled_section = False
            continue
        
        if filled_section and line.startswith('|') and not line.startswith('|--'):
            parts = [p.strip() for p in line.split('|')[1:-1]]  # Remove empty first and last
            if len(parts) >= 7 and parts[0].isdigit():
                chrom = parts[1]
                gap_id = int(parts[2])
                original_pos = parts[3]
                original_len = int(parts[4])
                fill_direction = parts[5]
                fill_length = int(parts[6])
                new_pos = parts[7]
                
                if chrom not in filled_info:
                    filled_info[chrom] = {}
                
                filled_info[chrom][gap_id] = {
                    'original_pos': original_pos,
                    'original_len': original_len,
                    'fill_direction': fill_direction,
                    'fill_length': fill_length,
                    'new_pos': new_pos
                }
        
        elif unfilled_section and line.startswith('|') and not line.startswith('|--'):
            parts = [p.strip() for p in line.split('|')[1:-1]]
            if len(parts) >= 5 and parts[0].isdigit():
                chrom = parts[1]
                gap_id = int(parts[2])
                position = parts[3]
                length = int(parts[4])
                reason = parts[5] if len(parts) > 5 else 'Unknown'
                
                if chrom not in unfilled_info:
                    unfilled_info[chrom] = {}
                
                unfilled_info[chrom][gap_id] = {
                    'position': position,
                    'length': length,
                    'reason': reason
                }
    
    return filled_info, unfilled_info

def analyze_fill_types(output_dir, gaps):
    """Analyze fill types by reading process.log files from DEGAP2.0_Output"""
    degap_output_dir = os.path.join(output_dir, 'DEGAP2.0_Output')
    if not os.path.exists(degap_output_dir):
        return gaps
    
    for chrom in gaps:
        for gap_id in gaps[chrom]:
            gap_name = f"{chrom}.{gap_id}"
            
            # Check both directions
            for direction in ['left', 'right']:
                result_dir = os.path.join(degap_output_dir, f"{gap_name}.{direction}")
                process_log = os.path.join(result_dir, 'process.log')
                
                if os.path.exists(process_log):
                    with open(process_log, 'r') as f:
                        content = f.read()
                    
                    if "GAP can be closed!" in content:
                        gaps[chrom][gap_id]['status'] = 'filled'
                        gaps[chrom][gap_id]['fill_direction'] = direction
                        
                        # Determine fill type
                        if "Direct overlap" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Direct Connection'
                        elif "Tandem repeat" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Tandem Repeat'
                        elif "Endloop!" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Extension'
                        else:
                            gaps[chrom][gap_id]['fill_type'] = 'Unknown'
                        
                        # Extract gap length and other details
                        for line in content.split('\n'):
                            if "GAP Length:" in line:
                                try:
                                    gap_length = int(line.split(':')[1].strip())
                                    gaps[chrom][gap_id]['fill_length'] = gap_length
                                except:
                                    pass
                            elif "extensionSequneceMinimumExtensionLength:" in line:
                                try:
                                    ext_len = int(line.split(':')[1].strip())
                                    gaps[chrom][gap_id]['extension_length'] = ext_len
                                except:
                                    pass
                        break
    
    return gaps

def get_chromosome_lengths(genome_file):
    """Get chromosome lengths from genome file"""
    chromosome_lengths = {}
    try:
        if os.path.exists(genome_file):
            with open(genome_file, 'r') as f:
                current_chrom = None
                current_length = 0
                
                for line in f:
                    line = line.strip()
                    if line.startswith('>'):
                        # Save previous chromosome
                        if current_chrom is not None:
                            chromosome_lengths[current_chrom] = current_length
                        
                        # Start new chromosome
                        current_chrom = line[1:].split()[0]  # Get first part of header
                        current_length = 0
                    else:
                        # Add sequence length
                        current_length += len(line)
                
                # Save last chromosome
                if current_chrom is not None:
                    chromosome_lengths[current_chrom] = current_length
            
            print(f"Successfully read chromosome lengths from {genome_file}")
        else:
            print(f"Warning: Genome file not found: {genome_file}")
    except Exception as e:
        print(f"Error reading genome file: {e}")
    return chromosome_lengths

def get_chromosome_lengths_fallback(gaps):
    """Fallback method to estimate chromosome lengths from gap positions"""
    chromosome_lengths = {}
    for chrom in gaps:
        if gaps[chrom]:
            # Use the last gap's end position + 1MB as chromosome length
            max_end = max(gap['end'] for gap in gaps[chrom].values())
            chromosome_lengths[chrom] = max_end + 1000000  # Add 1MB
            print(f"Using fallback length for {chrom}: {chromosome_lengths[chrom]:,} bp")
    return chromosome_lengths

def analyze_gap_length_distribution(gaps):
    """Analyze gap length distribution"""
    length_ranges = {
        'Very Small (< 100 bp)': 0,
        'Small (100-1K bp)': 0,
        'Medium (1K-10K bp)': 0,
        'Large (10K-50K bp)': 0,
        'Very Large (> 50K bp)': 0
    }
    
    filled_length_ranges = {
        'Very Small (< 100 bp)': 0,
        'Small (100-1K bp)': 0,
        'Medium (1K-10K bp)': 0,
        'Large (10K-50K bp)': 0,
        'Very Large (> 50K bp)': 0
    }
    
    for chrom in gaps:
        for gap_id in gaps[chrom]:
            gap = gaps[chrom][gap_id]
            gap_len = gap['gap_len']
            
            # Categorize by length
            if gap_len < 100:
                length_ranges['Very Small (< 100 bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Very Small (< 100 bp)'] += 1
            elif gap_len < 1000:
                length_ranges['Small (100-1K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Small (100-1K bp)'] += 1
            elif gap_len < 10000:
                length_ranges['Medium (1K-10K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Medium (1K-10K bp)'] += 1
            elif gap_len < 50000:
                length_ranges['Large (10K-50K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Large (10K-50K bp)'] += 1
            else:
                length_ranges['Very Large (> 50K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Very Large (> 50K bp)'] += 1
    
    return length_ranges, filled_length_ranges

def generate_html_report(gaps, chromosome_lengths, output_path):
    """Generate interactive HTML report"""
    
    # Calculate statistics
    total_gaps = sum(len(gaps[chrom]) for chrom in gaps)
    filled_gaps = sum(1 for chrom in gaps for gap_id in gaps[chrom] if gaps[chrom][gap_id]['status'] == 'filled')
    fill_rate = (filled_gaps / total_gaps * 100) if total_gaps > 0 else 0
    
    # Group by fill type
    fill_types = defaultdict(int)
    for chrom in gaps:
        for gap_id in gaps[chrom]:
            if gaps[chrom][gap_id]['status'] == 'filled':
                fill_type = gaps[chrom][gap_id]['fill_type'] or 'Unknown'
                fill_types[fill_type] += 1
    
    # Analyze gap length distribution
    length_ranges, filled_length_ranges = analyze_gap_length_distribution(gaps)

    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'SimHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .lang-switch {{
            position: absolute;
            top: 20px;
            right: 30px;
            display: flex;
            gap: 10px;
        }}
        .lang-btn {{
            padding: 5px 10px;
            border: 1px solid rgba(255,255,255,0.5);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9em;
        }}
        .lang-btn.active {{
            background: rgba(255,255,255,0.3);
            border-color: white;
        }}
        .lang-btn:hover {{
            background: rgba(255,255,255,0.2);
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .controls {{
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }}
        .chromosome-selector {{
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }}
        .chromosome-btn {{
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }}
        .chromosome-btn.active {{
            background: #667eea;
            color: white;
        }}
        .chromosome-btn:hover {{
            background: #667eea;
            color: white;
        }}
        .gap-visualization {{
            padding: 30px;
        }}
        .chromosome-section {{
            margin-bottom: 40px;
            display: none;
        }}
        .chromosome-section.active {{
            display: block;
        }}
        .chromosome-title {{
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }}
        .track-controls {{
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .track-control-group {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .track-control-btn {{
            padding: 5px 12px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9em;
        }}
        .track-control-btn.active {{
            background: #667eea;
            color: white;
        }}
        .track-control-btn:hover {{
            background: #667eea;
            color: white;
        }}
        .chromosome-tracks {{
            margin-bottom: 20px;
        }}
        .track-row {{
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }}
        .track-label {{
            width: 120px;
            font-size: 0.9em;
            color: #666;
            text-align: right;
            padding-right: 15px;
        }}
        .gap-track {{
            position: relative;
            height: 40px;
            background: #e9ecef;
            border-radius: 5px;
            flex: 1;
            overflow: visible;
        }}
        .gap-item {{
            position: absolute;
            height: 100%;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            font-weight: bold;
            min-width: 2px;
        }}
        .gap-item:hover {{
            transform: scaleY(1.3);
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }}
        .gap-filled {{
            background: #28a745;
        }}
        .gap-unfilled {{
            background: #dc3545;
        }}
        .legend {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }}
        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
        }}
        .tooltip.show {{
            opacity: 1;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="lang-switch">
                <div class="lang-btn active" onclick="switchLanguage('en')" data-lang="en">English</div>
                <div class="lang-btn" onclick="switchLanguage('zh')" data-lang="zh">中文</div>
            </div>
            <h1 data-en="DEGAP Gap Filling Report" data-zh="DEGAP 基因组Gap填充报告">DEGAP Gap Filling Report</h1>
            <p data-en="Interactive visualization of genome gap filling results" data-zh="基因组Gap填充结果交互式可视化">Interactive visualization of genome gap filling results</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_gaps}</div>
                <div class="stat-label" data-en="Total Gaps" data-zh="总Gap数">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{filled_gaps}</div>
                <div class="stat-label" data-en="Filled Gaps" data-zh="已填充Gap">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{fill_rate:.1f}%</div>
                <div class="stat-label" data-en="Fill Success Rate" data-zh="填充成功率">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(gaps)}</div>
                <div class="stat-label" data-en="Chromosomes" data-zh="染色体数">Chromosomes</div>
            </div>
        </div>"""

    # Add fill type statistics if available
    if fill_types:
        html_content += """
        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;" data-en="Fill Type Distribution" data-zh="填充类型分布">Fill Type Distribution</h3>"""

        # Fill type translations
        fill_type_translations = {
            'Extension': {'en': 'Extension', 'zh': '延申填充'},
            'Direct Connection': {'en': 'Direct Connection', 'zh': '直接连接'},
            'Tandem Repeat': {'en': 'Tandem Repeat', 'zh': '串联重复'},
            'Unknown': {'en': 'Unknown', 'zh': '未知类型'}
        }

        for fill_type, count in fill_types.items():
            percentage = (count / filled_gaps * 100) if filled_gaps > 0 else 0
            en_text = fill_type_translations.get(fill_type, {}).get('en', fill_type)
            zh_text = fill_type_translations.get(fill_type, {}).get('zh', fill_type)
            html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{count}</div>
                <div class="stat-label" data-en="{en_text}" data-zh="{zh_text}">{en_text}</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">{percentage:.1f}%</div>
            </div>"""

        html_content += """
        </div>"""



    for length_range, total_count in length_ranges.items():
        filled_count = filled_length_ranges[length_range]
        success_rate = (filled_count / total_count * 100) if total_count > 0 else 0
        html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{filled_count}/{total_count}</div>
                <div class="stat-label">{length_range}</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: {success_rate:.1f}%" data-zh="成功率: {success_rate:.1f}%">Success: {success_rate:.1f}%</div>
            </div>"""

    html_content += """
        </div>

        <div class="controls">
            <h3 data-en="Select Chromosome:" data-zh="选择染色体:">Select Chromosome:</h3>
            <div class="chromosome-selector">"""

    # Add chromosome buttons
    for chrom in sorted(gaps.keys()):
        html_content += f"""
                <button class="chromosome-btn" onclick="showChromosome('{chrom}')">{chrom}</button>"""

    html_content += """
            </div>
        </div>

        <div class="gap-visualization">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span data-en="Filled Gap" data-zh="已填充Gap">Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span data-en="Unfilled Gap" data-zh="未填充Gap">Unfilled Gap</span>
                </div>
            </div>"""

    # Add chromosome sections with multi-row display
    for chrom in sorted(gaps.keys()):
        chrom_gaps = gaps[chrom]
        if not chrom_gaps:
            continue

        # Get chromosome length
        chrom_length = chromosome_lengths.get(chrom, 0)
        if chrom_length == 0:
            # Fallback: use max gap end + 1MB
            chrom_length = max(gap['end'] for gap in chrom_gaps.values()) + 1000000

        html_content += f"""
            <div class="chromosome-section" id="chr-{chrom}">
                <div class="chromosome-title">{chrom} ({chrom_length:,} bp)</div>

                <div class="track-controls">
                    <div class="track-control-group">
                        <span data-en="Track Length:" data-zh="轨道长度:">Track Length:</span>
                        <div class="track-control-btn active" onclick="setTrackLength('{chrom}', 5000000)">5M</div>
                        <div class="track-control-btn" onclick="setTrackLength('{chrom}', 10000000)">10M</div>
                        <div class="track-control-btn" onclick="setTrackLength('{chrom}', 20000000)">20M</div>
                        <div class="track-control-btn" onclick="setTrackLength('{chrom}', 50000000)">50M</div>
                    </div>
                </div>

                <div class="chromosome-tracks" id="tracks-{chrom}">
                    <!-- Tracks will be generated by JavaScript -->
                </div>
            </div>"""

    # Add JavaScript and closing tags
    html_content += """
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Gap data and chromosome lengths for JavaScript
        const gapData = """ + json.dumps(gaps, indent=8) + """;
        const chromosomeLengths = """ + json.dumps(chromosome_lengths, indent=8) + """;

        // Language data
        const translations = {
            'Gap ID': {'en': 'Gap ID', 'zh': 'Gap编号'},
            'Position': {'en': 'Position', 'zh': '位置'},
            'Length': {'en': 'Length', 'zh': '长度'},
            'Status': {'en': 'Status', 'zh': '状态'},
            'Filled': {'en': 'Filled', 'zh': '已填充'},
            'Unfilled': {'en': 'Unfilled', 'zh': '未填充'},
            'Fill Type': {'en': 'Fill Type', 'zh': '填充类型'},
            'Fill Direction': {'en': 'Fill Direction', 'zh': '填充方向'},
            'Extension': {'en': 'Extension', 'zh': '延申填充'},
            'Direct Connection': {'en': 'Direct Connection', 'zh': '直接连接'},
            'Tandem Repeat': {'en': 'Tandem Repeat', 'zh': '串联重复'},
            'Unknown': {'en': 'Unknown', 'zh': '未知'}
        };

        let currentLanguage = 'en';
        let currentTrackLength = 5000000; // Default 5M

        function switchLanguage(lang) {
            currentLanguage = lang;

            // Update language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });

            // Update all translatable elements
            document.querySelectorAll('[data-en][data-zh]').forEach(element => {
                if (lang === 'zh') {
                    element.textContent = element.getAttribute('data-zh');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }

        function showChromosome(chromName) {
            // Hide all chromosome sections
            const sections = document.querySelectorAll('.chromosome-section');
            sections.forEach(section => section.classList.remove('active'));

            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.chromosome-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected chromosome
            const selectedSection = document.getElementById('chr-' + chromName);
            if (selectedSection) {
                selectedSection.classList.add('active');
                // Generate tracks for this chromosome
                generateChromosomeTracks(chromName, currentTrackLength);
            }

            // Activate selected button
            event.target.classList.add('active');
        }

        function setTrackLength(chromName, trackLength) {
            currentTrackLength = trackLength;

            // Update track control buttons
            const chromSection = document.getElementById('chr-' + chromName);
            if (chromSection) {
                const buttons = chromSection.querySelectorAll('.track-control-btn');
                buttons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.textContent === formatTrackLength(trackLength)) {
                        btn.classList.add('active');
                    }
                });
            }

            // Regenerate tracks
            generateChromosomeTracks(chromName, trackLength);
        }

        function formatTrackLength(length) {
            if (length >= 1000000) {
                return (length / 1000000) + 'M';
            } else if (length >= 1000) {
                return (length / 1000) + 'K';
            }
            return length.toString();
        }

        function generateChromosomeTracks(chromName, trackLength) {
            const tracksContainer = document.getElementById('tracks-' + chromName);
            if (!tracksContainer || !gapData[chromName]) return;

            const chromLength = chromosomeLengths[chromName] || 0;
            const gaps = gapData[chromName];

            // Calculate number of tracks needed
            const numTracks = Math.ceil(chromLength / trackLength);

            let tracksHTML = '';

            for (let trackIndex = 0; trackIndex < numTracks; trackIndex++) {
                const trackStart = trackIndex * trackLength;
                const trackEnd = Math.min((trackIndex + 1) * trackLength, chromLength);
                const actualTrackLength = trackEnd - trackStart;
                const trackWidthPercent = (actualTrackLength / trackLength) * 100;

                // Format track label
                const startLabel = formatPosition(trackStart);
                const endLabel = formatPosition(trackEnd);

                tracksHTML += `
                    <div class="track-row">
                        <div class="track-label">${startLabel} - ${endLabel}</div>
                        <div class="gap-track" style="width: ${trackWidthPercent}%;">`;

                // Add gaps that fall within this track
                Object.keys(gaps).forEach(gapId => {
                    const gap = gaps[gapId];
                    const gapStart = gap.start;
                    const gapEnd = gap.end;

                    // Check if gap overlaps with this track
                    if (gapEnd >= trackStart && gapStart < trackEnd) {
                        const relativeStart = Math.max(0, gapStart - trackStart);
                        const relativeEnd = Math.min(actualTrackLength, gapEnd - trackStart);
                        const leftPercent = (relativeStart / actualTrackLength) * 100;
                        const widthPercent = ((relativeEnd - relativeStart) / actualTrackLength) * 100;

                        const statusClass = gap.status === 'filled' ? 'gap-filled' : 'gap-unfilled';
                        const fillType = gap.fill_type || 'Unknown';
                        const fillTypeTranslated = translations[fillType] ? translations[fillType][currentLanguage] : fillType;
                        const statusTranslated = gap.status === 'filled' ?
                            translations['Filled'][currentLanguage] :
                            translations['Unfilled'][currentLanguage];

                        tracksHTML += `
                            <div class="gap-item ${statusClass}"
                                 style="left: ${leftPercent.toFixed(2)}%; width: ${widthPercent.toFixed(2)}%;"
                                 onmouseover="showTooltip(event, '${chromName}', ${gapId})"
                                 onmouseout="hideTooltip()"
                                 onclick="window.open('https://www.baidu.com', '_blank')"
                                 data-gap-id="${gapId}"
                                 data-gap-start="${gapStart}"
                                 data-gap-end="${gapEnd}"
                                 data-gap-length="${gap.gap_len}"
                                 data-gap-status="${gap.status}"
                                 data-gap-type="${fillType}">
                                ${gapId}
                            </div>`;
                    }
                });

                tracksHTML += `
                        </div>
                    </div>`;
            }

            tracksContainer.innerHTML = tracksHTML;
        }

        function formatPosition(pos) {
            if (pos >= 1000000) {
                return (pos / 1000000).toFixed(1) + 'M';
            } else if (pos >= 1000) {
                return (pos / 1000).toFixed(0) + 'K';
            }
            return pos.toString();
        }

        function showTooltip(event, chromName, gapId) {
            const tooltip = document.getElementById('tooltip');
            const gap = gapData[chromName][gapId];

            if (!gap || !tooltip) return;

            const fillType = gap.fill_type || 'Unknown';
            const fillTypeTranslated = translations[fillType] ? translations[fillType][currentLanguage] : fillType;
            const statusTranslated = gap.status === 'filled' ?
                translations['Filled'][currentLanguage] :
                translations['Unfilled'][currentLanguage];

            const positionText = translations['Position'][currentLanguage];
            const lengthText = translations['Length'][currentLanguage];
            const statusText = translations['Status'][currentLanguage];
            const fillTypeText = translations['Fill Type'][currentLanguage];

            tooltip.innerHTML = `
                <strong>Gap ${gapId}</strong><br>
                ${positionText}: ${gap.start.toLocaleString()} - ${gap.end.toLocaleString()}<br>
                ${lengthText}: ${gap.gap_len.toLocaleString()} bp<br>
                ${statusText}: ${statusTranslated}<br>
                ${gap.status === 'filled' ? fillTypeText + ': ' + fillTypeTranslated : ''}
            `;

            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.classList.add('show');
        }

        function hideTooltip() {
            const tooltip = document.getElementById('tooltip');
            if (tooltip) {
                tooltip.classList.remove('show');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show first chromosome by default
            const firstBtn = document.querySelector('.chromosome-btn');
            if (firstBtn) {
                firstBtn.click();
            }
        });
    </script>
</body>
</html>"""

    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

def main():
    parser = argparse.ArgumentParser(description='Generate DEGAP HTML report')
    parser.add_argument('--output-dir', '-o', default='.',
                       help='DEGAP output directory (default: current directory)')
    parser.add_argument('--html-output', default='degap_report.html',
                       help='Output HTML file name (default: degap_report.html)')
    parser.add_argument('--genome', '-g',
                       help='Genome file path (for accurate chromosome lengths)')

    args = parser.parse_args()

    output_dir = os.path.abspath(args.output_dir)

    # Check if required files exist
    gap_log = os.path.join(output_dir, 'gap.log')
    detailed_log = os.path.join(output_dir, 'detailed_process.log')

    if not os.path.exists(gap_log):
        print(f"Error: Cannot find gap.log in {output_dir}")
        sys.exit(1)

    print("Parsing gap information...")
    gaps = parse_gap_log(gap_log)

    print("Parsing detailed process log...")
    filled_info, unfilled_info = parse_detailed_process_log(detailed_log)

    print("Analyzing fill types...")
    gaps = analyze_fill_types(output_dir, gaps)

    # Update gaps with detailed information
    for chrom in filled_info:
        if chrom in gaps:
            for gap_id in filled_info[chrom]:
                if gap_id in gaps[chrom]:
                    info = filled_info[chrom][gap_id]
                    gaps[chrom][gap_id]['status'] = 'filled'
                    gaps[chrom][gap_id]['fill_direction'] = info['fill_direction']
                    gaps[chrom][gap_id]['fill_length'] = info['fill_length']

    # Get chromosome lengths
    print("Getting chromosome lengths...")
    chromosome_lengths = {}
    if args.genome and os.path.exists(args.genome):
        chromosome_lengths = get_chromosome_lengths(args.genome)

    # Use fallback method if no genome file or failed to read
    if not chromosome_lengths:
        print("Using fallback method for chromosome lengths...")
        chromosome_lengths = get_chromosome_lengths_fallback(gaps)

    html_output = os.path.join(output_dir, args.html_output)
    print(f"Generating HTML report: {html_output}")
    generate_html_report(gaps, chromosome_lengths, html_output)

    print(f"HTML report generated successfully: {html_output}")

if __name__ == '__main__':
    main()

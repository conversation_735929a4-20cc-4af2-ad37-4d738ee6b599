# DEGAP HTML可视化报告使用说明

## 功能完成情况

✅ **所有要求功能已完成**

### 1. 中英文切换功能
- 右上角语言切换按钮：English / 中文
- 支持所有界面元素的中英文切换
- 中文字体优化，支持Windows/Mac/Linux系统

### 2. 染色体位置图正确显示
- 多行显示，默认每行5M长度
- 可调节轨道长度：5M、10M、20M、50M
- 最后一行按实际长度比例显示，不占满整行
- 精确的位置标注和比例显示

### 3. 染色体长度获取
- 优先从`--genome`参数指定的基因组文件读取真实长度
- 如果没有基因组文件，使用fallback方法：最后一个gap位置 + 1MB

### 4. 多行显示控制
- 轨道长度控制按钮：5M、10M、20M、50M
- 动态调整显示密度
- 智能换行，最后一行按比例显示

### 5. Gap位点交互功能
- **悬停提示**：显示Gap编号、位置、长度、填充状态、填充类型
- **点击跳转**：点击Gap可跳转到百度官网（占位功能）
- **颜色编码**：绿色=已填充，红色=未填充

## 使用方法

### 基本用法
```bash
# 在DEGAP输出目录中运行
python generate_degap_html_final.py --output-dir . --html-output degap_report.html
```

### 完整用法（推荐）
```bash
# 指定基因组文件以获取准确的染色体长度
python generate_degap_html_final.py \
    --output-dir /path/to/degap/output \
    --genome /path/to/genome.fasta \
    --html-output degap_visualization.html
```

### 参数说明
- `--output-dir` / `-o`：DEGAP输出目录路径
- `--html-output`：输出HTML文件名
- `--genome` / `-g`：基因组文件路径（可选，用于获取准确的染色体长度）

## 输入文件要求

### 必需文件
- `gap.log`：原始gap信息
- `detailed_process.log`：详细处理结果

### 可选文件
- `DEGAP2.0_Output/`：各个gap的详细结果目录
- 基因组FASTA文件：用于获取准确的染色体长度

## 界面功能说明

### 1. 统计概览
- 总Gap数量
- 已填充Gap数量  
- 填充成功率
- 染色体数量

### 2. 填充类型分布
- **延申填充**（Extension）：通过序列延申填充
- **直接连接**（Direct Connection）：左右序列直接重叠
- **串联重复**（Tandem Repeat）：串联重复序列填充
- **未知类型**（Unknown）：其他类型

### 3. Gap长度分布
- Very Small (< 100 bp)
- Small (100-1K bp)
- Medium (1K-10K bp)
- Large (10K-50K bp)
- Very Large (> 50K bp)

每个类别显示总数和填充成功率。

### 4. 染色体可视化
- **染色体选择**：点击按钮切换不同染色体
- **轨道长度控制**：5M/10M/20M/50M选项
- **多行显示**：自动分行显示，便于查看长染色体
- **Gap标注**：每个Gap显示编号，颜色表示状态

### 5. 交互功能
- **悬停提示**：鼠标悬停显示详细信息
- **点击跳转**：点击Gap跳转到外部链接
- **语言切换**：右上角切换中英文
- **响应式设计**：适配不同屏幕尺寸

## 技术特性

- **纯HTML文件**：无需服务器，直接浏览器打开
- **UTF-8编码**：完美支持中文显示
- **现代CSS**：渐变色、阴影、动画效果
- **JavaScript交互**：动态内容生成和用户交互
- **数据驱动**：基于实际DEGAP输出数据

## 故障排除

### 中文显示异常
- 确保浏览器支持UTF-8编码
- 清除浏览器缓存后重新打开
- 使用现代浏览器（Chrome、Firefox、Safari、Edge）

### 染色体不显示
- 检查`gap.log`文件是否存在且格式正确
- 确保至少有一个染色体包含gap数据

### 位置显示不准确
- 使用`--genome`参数指定基因组文件
- 检查基因组文件格式是否为标准FASTA

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+  
- ✅ Safari 12+
- ✅ Edge 79+

## 示例输出

生成的HTML报告包含：
1. 美观的统计卡片展示
2. 交互式染色体可视化
3. 多语言支持界面
4. 详细的Gap信息提示
5. 响应式布局设计

报告文件可以直接分享给其他用户，无需任何额外依赖。

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', 'PingFang SC', 'Hiragino Sans GB', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .lang-switch {
            position: absolute;
            top: 20px;
            right: 30px;
            display: flex;
            gap: 10px;
        }
        .lang-btn {
            padding: 5px 10px;
            border: 1px solid rgba(255,255,255,0.5);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9em;
        }
        .lang-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: white;
        }
        .lang-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .controls {
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .chromosome-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .chromosome-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .chromosome-btn.active {
            background: #667eea;
            color: white;
        }
        .chromosome-btn:hover {
            background: #667eea;
            color: white;
        }
        .gap-visualization {
            padding: 30px;
        }
        .chromosome-section {
            margin-bottom: 40px;
            display: none;
        }
        .chromosome-section.active {
            display: block;
        }
        .chromosome-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        .track-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .track-control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .track-control-btn {
            padding: 5px 12px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9em;
        }
        .track-control-btn.active {
            background: #667eea;
            color: white;
        }
        .track-control-btn:hover {
            background: #667eea;
            color: white;
        }
        .chromosome-tracks {
            margin-bottom: 20px;
        }
        .track-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .track-label {
            width: 120px;
            font-size: 0.9em;
            color: #666;
            text-align: right;
            padding-right: 15px;
        }
        .gap-track {
            position: relative;
            height: 40px;
            background: #e9ecef;
            border-radius: 5px;
            flex: 1;
            overflow: visible;
        }
        .gap-item {
            position: absolute;
            height: 100%;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            font-weight: bold;
            min-width: 2px;
        }
        .gap-item:hover {
            transform: scaleY(1.3);
            z-index: 10;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }
        .gap-filled {
            background: #28a745;
        }
        .gap-unfilled {
            background: #dc3545;
        }
        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .tooltip.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="lang-switch">
                <div class="lang-btn active" onclick="switchLanguage('en')" data-lang="en">English</div>
                <div class="lang-btn" onclick="switchLanguage('zh')" data-lang="zh">中文</div>
            </div>
            <h1 data-en="DEGAP Gap Filling Report" data-zh="DEGAP 基因组Gap填充报告">DEGAP Gap Filling Report</h1>
            <p data-en="Interactive visualization of genome gap filling results" data-zh="基因组Gap填充结果交互式可视化">Interactive visualization of genome gap filling results</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">20</div>
                <div class="stat-label" data-en="Total Gaps" data-zh="总Gap数">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label" data-en="Filled Gaps" data-zh="已填充Gap">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">60.0%</div>
                <div class="stat-label" data-en="Fill Success Rate" data-zh="填充成功率">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label" data-en="Chromosomes" data-zh="染色体数">Chromosomes</div>
            </div>
        </div>
        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;" data-en="Fill Type Distribution" data-zh="填充类型分布">Fill Type Distribution</h3>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label" data-en="Unknown" data-zh="未知类型">Unknown</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">100.0%</div>
            </div>
        </div>
        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;" data-en="Gap Length Distribution" data-zh="Gap长度分布">Gap Length Distribution</h3>
            <div class="stat-card">
                <div class="stat-number">2/4</div>
                <div class="stat-label">Very Small (< 100 bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: 50.0%" data-zh="成功率: 50.0%">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4/4</div>
                <div class="stat-label">Small (100-1K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: 100.0%" data-zh="成功率: 100.0%">Success: 100.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">5/10</div>
                <div class="stat-label">Medium (1K-10K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: 50.0%" data-zh="成功率: 50.0%">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1/2</div>
                <div class="stat-label">Large (10K-50K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: 50.0%" data-zh="成功率: 50.0%">Success: 50.0%</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0/0</div>
                <div class="stat-label">Very Large (> 50K bp)</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;" data-en="Success: 0.0%" data-zh="成功率: 0.0%">Success: 0.0%</div>
            </div>
        </div>

        <div class="controls">
            <h3 data-en="Select Chromosome:" data-zh="选择染色体:">Select Chromosome:</h3>
            <div class="chromosome-selector">
                <button class="chromosome-btn" onclick="showChromosome('chr1A_TA299')">chr1A_TA299</button>
            </div>
        </div>

        <div class="gap-visualization">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span data-en="Filled Gap" data-zh="已填充Gap">Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span data-en="Unfilled Gap" data-zh="未填充Gap">Unfilled Gap</span>
                </div>
            </div>
            <div class="chromosome-section" id="chr-chr1A_TA299">
                <div class="chromosome-title">chr1A_TA299 (636,944,789 bp)</div>

                <div class="track-controls">
                    <div class="track-control-group">
                        <span data-en="Track Length:" data-zh="轨道长度:">Track Length:</span>
                        <div class="track-control-btn active" onclick="setTrackLength('chr1A_TA299', 5000000)">5M</div>
                        <div class="track-control-btn" onclick="setTrackLength('chr1A_TA299', 10000000)">10M</div>
                        <div class="track-control-btn" onclick="setTrackLength('chr1A_TA299', 20000000)">20M</div>
                        <div class="track-control-btn" onclick="setTrackLength('chr1A_TA299', 50000000)">50M</div>
                    </div>
                </div>

                <div class="chromosome-tracks" id="tracks-chr1A_TA299">
                    <!-- Tracks will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Gap data and chromosome lengths for JavaScript
        const gapData = {
        "chr1A_TA299": {
                "1": {
                        "start": 138402,
                        "end": 138424,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 138402,
                        "new_end": 138424
                },
                "2": {
                        "start": 254354,
                        "end": 272628,
                        "gap_len": 18275,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 254354,
                        "new_end": 272628
                },
                "3": {
                        "start": 30054223,
                        "end": 30054322,
                        "gap_len": 100,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1796,
                        "fill_type": null,
                        "new_start": 30054223,
                        "new_end": 30054322
                },
                "4": {
                        "start": 42926970,
                        "end": 42976731,
                        "gap_len": 49762,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 113258,
                        "fill_type": null,
                        "new_start": 42926970,
                        "new_end": 42976731
                },
                "5": {
                        "start": 57693457,
                        "end": 57694440,
                        "gap_len": 984,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 1176,
                        "fill_type": null,
                        "new_start": 57693457,
                        "new_end": 57694440
                },
                "6": {
                        "start": 71479355,
                        "end": 71482452,
                        "gap_len": 3098,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": -1214,
                        "fill_type": null,
                        "new_start": 71479355,
                        "new_end": 71482452
                },
                "7": {
                        "start": 110023940,
                        "end": 110027276,
                        "gap_len": 3337,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 110023940,
                        "new_end": 110027276
                },
                "8": {
                        "start": 150338264,
                        "end": 150343236,
                        "gap_len": 4973,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -10543,
                        "fill_type": null,
                        "new_start": 150338264,
                        "new_end": 150343236
                },
                "9": {
                        "start": 159684345,
                        "end": 159685915,
                        "gap_len": 1571,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 1332,
                        "fill_type": null,
                        "new_start": 159684345,
                        "new_end": 159685915
                },
                "10": {
                        "start": 182271447,
                        "end": 182271469,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 182271447,
                        "new_end": 182271469
                },
                "11": {
                        "start": 207927380,
                        "end": 207929943,
                        "gap_len": 2564,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 207927380,
                        "new_end": 207929943
                },
                "12": {
                        "start": 286215887,
                        "end": 286218916,
                        "gap_len": 3030,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": 3117,
                        "fill_type": null,
                        "new_start": 286215887,
                        "new_end": 286218916
                },
                "13": {
                        "start": 305356130,
                        "end": 305357762,
                        "gap_len": 1633,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 305356130,
                        "new_end": 305357762
                },
                "14": {
                        "start": 431979574,
                        "end": 431982803,
                        "gap_len": 3230,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 2635,
                        "fill_type": null,
                        "new_start": 431979574,
                        "new_end": 431982803
                },
                "15": {
                        "start": 606694072,
                        "end": 606694094,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1026,
                        "fill_type": null,
                        "new_start": 606694072,
                        "new_end": 606694094
                },
                "16": {
                        "start": 613338462,
                        "end": 613342080,
                        "gap_len": 3619,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 613338462,
                        "new_end": 613342080
                },
                "17": {
                        "start": 617024912,
                        "end": 617025585,
                        "gap_len": 674,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "right",
                        "fill_length": 723,
                        "fill_type": null,
                        "new_start": 617024912,
                        "new_end": 617025585
                },
                "18": {
                        "start": 617827052,
                        "end": 617830319,
                        "gap_len": 3268,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "unfilled",
                        "fill_direction": null,
                        "fill_length": null,
                        "fill_type": null,
                        "new_start": 617827052,
                        "new_end": 617830319
                },
                "19": {
                        "start": 632889350,
                        "end": 632890236,
                        "gap_len": 887,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -1263,
                        "fill_type": null,
                        "new_start": 632889350,
                        "new_end": 632890236
                },
                "20": {
                        "start": 635944767,
                        "end": 635944789,
                        "gap_len": 23,
                        "left_len": 59799,
                        "right_len": 59799,
                        "status": "filled",
                        "fill_direction": "left",
                        "fill_length": -3802,
                        "fill_type": null,
                        "new_start": 635944767,
                        "new_end": 635944789
                }
        }
};
        const chromosomeLengths = {
        "chr1A_TA299": 636944789
};

        // Language data
        const translations = {
            'Gap ID': {'en': 'Gap ID', 'zh': 'Gap编号'},
            'Position': {'en': 'Position', 'zh': '位置'},
            'Length': {'en': 'Length', 'zh': '长度'},
            'Status': {'en': 'Status', 'zh': '状态'},
            'Filled': {'en': 'Filled', 'zh': '已填充'},
            'Unfilled': {'en': 'Unfilled', 'zh': '未填充'},
            'Fill Type': {'en': 'Fill Type', 'zh': '填充类型'},
            'Fill Direction': {'en': 'Fill Direction', 'zh': '填充方向'},
            'Extension': {'en': 'Extension', 'zh': '延申填充'},
            'Direct Connection': {'en': 'Direct Connection', 'zh': '直接连接'},
            'Tandem Repeat': {'en': 'Tandem Repeat', 'zh': '串联重复'},
            'Unknown': {'en': 'Unknown', 'zh': '未知'}
        };

        let currentLanguage = 'en';
        let currentTrackLength = 5000000; // Default 5M

        function switchLanguage(lang) {
            currentLanguage = lang;

            // Update language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute('data-lang') === lang) {
                    btn.classList.add('active');
                }
            });

            // Update all translatable elements
            document.querySelectorAll('[data-en][data-zh]').forEach(element => {
                if (lang === 'zh') {
                    element.textContent = element.getAttribute('data-zh');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }

        function showChromosome(chromName) {
            // Hide all chromosome sections
            const sections = document.querySelectorAll('.chromosome-section');
            sections.forEach(section => section.classList.remove('active'));

            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.chromosome-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected chromosome
            const selectedSection = document.getElementById('chr-' + chromName);
            if (selectedSection) {
                selectedSection.classList.add('active');
                // Generate tracks for this chromosome
                generateChromosomeTracks(chromName, currentTrackLength);
            }

            // Activate selected button
            event.target.classList.add('active');
        }

        function setTrackLength(chromName, trackLength) {
            currentTrackLength = trackLength;

            // Update track control buttons
            const chromSection = document.getElementById('chr-' + chromName);
            if (chromSection) {
                const buttons = chromSection.querySelectorAll('.track-control-btn');
                buttons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.textContent === formatTrackLength(trackLength)) {
                        btn.classList.add('active');
                    }
                });
            }

            // Regenerate tracks
            generateChromosomeTracks(chromName, trackLength);
        }

        function formatTrackLength(length) {
            if (length >= 1000000) {
                return (length / 1000000) + 'M';
            } else if (length >= 1000) {
                return (length / 1000) + 'K';
            }
            return length.toString();
        }

        function generateChromosomeTracks(chromName, trackLength) {
            const tracksContainer = document.getElementById('tracks-' + chromName);
            if (!tracksContainer || !gapData[chromName]) return;

            const chromLength = chromosomeLengths[chromName] || 0;
            const gaps = gapData[chromName];

            // Calculate number of tracks needed
            const numTracks = Math.ceil(chromLength / trackLength);

            let tracksHTML = '';

            for (let trackIndex = 0; trackIndex < numTracks; trackIndex++) {
                const trackStart = trackIndex * trackLength;
                const trackEnd = Math.min((trackIndex + 1) * trackLength, chromLength);
                const actualTrackLength = trackEnd - trackStart;
                const trackWidthPercent = (actualTrackLength / trackLength) * 100;

                // Format track label
                const startLabel = formatPosition(trackStart);
                const endLabel = formatPosition(trackEnd);

                tracksHTML += `
                    <div class="track-row">
                        <div class="track-label">${startLabel} - ${endLabel}</div>
                        <div class="gap-track" style="width: ${trackWidthPercent}%;">`;

                // Add gaps that fall within this track
                Object.keys(gaps).forEach(gapId => {
                    const gap = gaps[gapId];
                    const gapStart = gap.start;
                    const gapEnd = gap.end;

                    // Check if gap overlaps with this track
                    if (gapEnd >= trackStart && gapStart < trackEnd) {
                        const relativeStart = Math.max(0, gapStart - trackStart);
                        const relativeEnd = Math.min(actualTrackLength, gapEnd - trackStart);
                        const leftPercent = (relativeStart / actualTrackLength) * 100;
                        const widthPercent = ((relativeEnd - relativeStart) / actualTrackLength) * 100;

                        const statusClass = gap.status === 'filled' ? 'gap-filled' : 'gap-unfilled';
                        const fillType = gap.fill_type || 'Unknown';
                        const fillTypeTranslated = translations[fillType] ? translations[fillType][currentLanguage] : fillType;
                        const statusTranslated = gap.status === 'filled' ?
                            translations['Filled'][currentLanguage] :
                            translations['Unfilled'][currentLanguage];

                        tracksHTML += `
                            <div class="gap-item ${statusClass}"
                                 style="left: ${leftPercent.toFixed(2)}%; width: ${widthPercent.toFixed(2)}%;"
                                 onmouseover="showTooltip(event, '${chromName}', ${gapId})"
                                 onmouseout="hideTooltip()"
                                 onclick="window.open('https://www.baidu.com', '_blank')"
                                 data-gap-id="${gapId}"
                                 data-gap-start="${gapStart}"
                                 data-gap-end="${gapEnd}"
                                 data-gap-length="${gap.gap_len}"
                                 data-gap-status="${gap.status}"
                                 data-gap-type="${fillType}">
                                ${gapId}
                            </div>`;
                    }
                });

                tracksHTML += `
                        </div>
                    </div>`;
            }

            tracksContainer.innerHTML = tracksHTML;
        }

        function formatPosition(pos) {
            if (pos >= 1000000) {
                return (pos / 1000000).toFixed(1) + 'M';
            } else if (pos >= 1000) {
                return (pos / 1000).toFixed(0) + 'K';
            }
            return pos.toString();
        }

        function showTooltip(event, chromName, gapId) {
            const tooltip = document.getElementById('tooltip');
            const gap = gapData[chromName][gapId];

            if (!gap || !tooltip) return;

            const fillType = gap.fill_type || 'Unknown';
            const fillTypeTranslated = translations[fillType] ? translations[fillType][currentLanguage] : fillType;
            const statusTranslated = gap.status === 'filled' ?
                translations['Filled'][currentLanguage] :
                translations['Unfilled'][currentLanguage];

            const positionText = translations['Position'][currentLanguage];
            const lengthText = translations['Length'][currentLanguage];
            const statusText = translations['Status'][currentLanguage];
            const fillTypeText = translations['Fill Type'][currentLanguage];

            tooltip.innerHTML = `
                <strong>Gap ${gapId}</strong><br>
                ${positionText}: ${gap.start.toLocaleString()} - ${gap.end.toLocaleString()}<br>
                ${lengthText}: ${gap.gap_len.toLocaleString()} bp<br>
                ${statusText}: ${statusTranslated}<br>
                ${gap.status === 'filled' ? fillTypeText + ': ' + fillTypeTranslated : ''}
            `;

            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.classList.add('show');
        }

        function hideTooltip() {
            const tooltip = document.getElementById('tooltip');
            if (tooltip) {
                tooltip.classList.remove('show');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show first chromosome by default
            const firstBtn = document.querySelector('.chromosome-btn');
            if (firstBtn) {
                firstBtn.click();
            }
        });
    </script>
</body>
</html>
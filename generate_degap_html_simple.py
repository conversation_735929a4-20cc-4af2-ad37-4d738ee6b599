#!/usr/bin/env python3
"""
DEGAP HTML Report Generator - Simplified Version
For integration into AutoGapfiller post-processing
"""

import os
import sys
import json
from collections import defaultdict

def generate_degap_html_report(output_dir, html_filename='degap_report.html'):
    """
    Generate HTML report for DEGAP results
    
    Args:
        output_dir: DEGAP output directory path
        html_filename: Output HTML filename
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    try:
        # Parse gap.log
        gap_log_path = os.path.join(output_dir, 'gap.log')
        if not os.path.exists(gap_log_path):
            print(f"Warning: gap.log not found in {output_dir}")
            return False
        
        gaps = {}
        with open(gap_log_path, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 7:
                    chrom = parts[0]
                    gap_id = int(parts[1])
                    start = int(parts[2])
                    end = int(parts[3])
                    gap_len = int(parts[4])
                    
                    if chrom not in gaps:
                        gaps[chrom] = {}
                    
                    gaps[chrom][gap_id] = {
                        'start': start,
                        'end': end,
                        'gap_len': gap_len,
                        'status': 'unfilled',
                        'fill_type': None,
                        'fill_direction': None
                    }
        
        # Parse detailed_process.log if exists
        detailed_log_path = os.path.join(output_dir, 'detailed_process.log')
        if os.path.exists(detailed_log_path):
            with open(detailed_log_path, 'r') as f:
                content = f.read()
            
            # Parse filled gaps
            filled_section = False
            for line in content.split('\n'):
                line = line.strip()
                
                if line.startswith('## Filled Gap Details'):
                    filled_section = True
                    continue
                elif line.startswith('## '):
                    filled_section = False
                    continue
                
                if filled_section and line.startswith('|') and not line.startswith('|--'):
                    parts = [p.strip() for p in line.split('|')[1:-1]]
                    if len(parts) >= 6 and parts[0].isdigit():
                        chrom = parts[1]
                        gap_id = int(parts[2])
                        fill_direction = parts[5]
                        
                        if chrom in gaps and gap_id in gaps[chrom]:
                            gaps[chrom][gap_id]['status'] = 'filled'
                            gaps[chrom][gap_id]['fill_direction'] = fill_direction
        
        # Analyze fill types from process.log files
        degap_output_dir = os.path.join(output_dir, 'DEGAP2.0_Output')
        if os.path.exists(degap_output_dir):
            for chrom in gaps:
                for gap_id in gaps[chrom]:
                    gap_name = f"{chrom}.{gap_id}"
                    
                    for direction in ['left', 'right']:
                        result_dir = os.path.join(degap_output_dir, f"{gap_name}.{direction}")
                        process_log = os.path.join(result_dir, 'process.log')
                        
                        if os.path.exists(process_log):
                            with open(process_log, 'r') as f:
                                content = f.read()
                            
                            if "GAP can be closed!" in content:
                                gaps[chrom][gap_id]['status'] = 'filled'
                                gaps[chrom][gap_id]['fill_direction'] = direction
                                
                                if "Direct overlap" in content:
                                    gaps[chrom][gap_id]['fill_type'] = 'Direct Connection'
                                elif "Tandem repeat" in content:
                                    gaps[chrom][gap_id]['fill_type'] = 'Tandem Repeat'
                                elif "Endloop!" in content:
                                    gaps[chrom][gap_id]['fill_type'] = 'Extension'
                                else:
                                    gaps[chrom][gap_id]['fill_type'] = 'Unknown'
                                break
        
        # Calculate statistics
        total_gaps = sum(len(gaps[chrom]) for chrom in gaps)
        filled_gaps = sum(1 for chrom in gaps for gap_id in gaps[chrom] if gaps[chrom][gap_id]['status'] == 'filled')
        fill_rate = (filled_gaps / total_gaps * 100) if total_gaps > 0 else 0
        
        # Group by fill type
        fill_types = defaultdict(int)
        for chrom in gaps:
            for gap_id in gaps[chrom]:
                if gaps[chrom][gap_id]['status'] == 'filled':
                    fill_type = gaps[chrom][gap_id]['fill_type'] or 'Unknown'
                    fill_types[fill_type] += 1
        
        # Generate HTML
        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body {{ font-family: 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .header h1 {{ margin: 0; font-size: 2.5em; font-weight: 300; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; background: #f8f9fa; }}
        .stat-card {{ background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #667eea; }}
        .stat-label {{ color: #666; margin-top: 5px; }}
        .chromosome-section {{ padding: 30px; }}
        .chromosome-title {{ font-size: 1.5em; font-weight: bold; margin-bottom: 20px; color: #333; }}
        .gap-track {{ position: relative; height: 60px; background: #e9ecef; border-radius: 5px; margin-bottom: 20px; }}
        .gap-item {{ position: absolute; height: 100%; border-radius: 3px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold; }}
        .gap-filled {{ background: #28a745; }}
        .gap-unfilled {{ background: #dc3545; }}
        .legend {{ display: flex; gap: 20px; margin-bottom: 20px; }}
        .legend-item {{ display: flex; align-items: center; gap: 8px; }}
        .legend-color {{ width: 20px; height: 20px; border-radius: 3px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DEGAP Gap Filling Report</h1>
            <p>Genome gap filling results visualization</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_gaps}</div>
                <div class="stat-label">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{filled_gaps}</div>
                <div class="stat-label">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{fill_rate:.1f}%</div>
                <div class="stat-label">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(gaps)}</div>
                <div class="stat-label">Chromosomes</div>
            </div>
        </div>"""
        
        if fill_types:
            html_content += """
        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Fill Type Distribution</h3>"""
            
            for fill_type, count in fill_types.items():
                percentage = (count / filled_gaps * 100) if filled_gaps > 0 else 0
                html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{count}</div>
                <div class="stat-label">{fill_type}</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">{percentage:.1f}%</div>
            </div>"""
            
            html_content += """
        </div>"""
        
        # Add chromosome visualizations
        for chrom in sorted(gaps.keys()):
            chrom_gaps = gaps[chrom]
            if not chrom_gaps:
                continue
                
            max_pos = max(gap['end'] for gap in chrom_gaps.values())
            
            html_content += f"""
        <div class="chromosome-section">
            <div class="chromosome-title">{chrom}</div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span>Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span>Unfilled Gap</span>
                </div>
            </div>
            <div class="gap-track">"""
            
            for gap_id in sorted(chrom_gaps.keys()):
                gap = chrom_gaps[gap_id]
                left_percent = (gap['start'] / max_pos) * 100
                width_percent = ((gap['end'] - gap['start']) / max_pos) * 100
                
                status_class = 'gap-filled' if gap['status'] == 'filled' else 'gap-unfilled'
                fill_info = f" ({gap['fill_type']})" if gap['fill_type'] else ""
                
                html_content += f"""
                <div class="gap-item {status_class}" 
                     style="left: {left_percent:.2f}%; width: {width_percent:.2f}%;"
                     title="Gap {gap_id}: {gap['start']:,}-{gap['end']:,} ({gap['gap_len']:,} bp){fill_info}">
                    {gap_id}
                </div>"""
            
            html_content += """
            </div>
        </div>"""
        
        html_content += """
    </div>
</body>
</html>"""
        
        # Write HTML file
        html_path = os.path.join(output_dir, html_filename)
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML report generated: {html_path}")
        return True
        
    except Exception as e:
        print(f"Error generating HTML report: {e}")
        return False

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate DEGAP HTML report')
    parser.add_argument('--output-dir', '-o', default='.', help='DEGAP output directory')
    parser.add_argument('--html-output', default='degap_report.html', help='Output HTML filename')
    
    args = parser.parse_args()
    
    success = generate_degap_html_report(args.output_dir, args.html_output)
    sys.exit(0 if success else 1)

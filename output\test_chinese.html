<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chinese Test</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', 'Segoe UI', sans-serif;
            padding: 20px;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
        }
        .lang-btn {
            padding: 5px 10px;
            margin: 5px;
            border: 1px solid #667eea;
            background: white;
            color: #667eea;
            cursor: pointer;
        }
        .lang-btn.active {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <h1>中文显示测试</h1>
    
    <div>
        <button class="lang-btn active" onclick="switchLanguage('en')">English</button>
        <button class="lang-btn" onclick="switchLanguage('zh')">中文</button>
    </div>
    
    <div class="test-item">
        <h2 data-en="DEGAP Gap Filling Report" data-zh="DEGAP 基因组Gap填充报告">DEGAP Gap Filling Report</h2>
    </div>
    
    <div class="test-item">
        <span data-en="Total Gaps" data-zh="总Gap数">Total Gaps</span>
    </div>
    
    <div class="test-item">
        <span data-en="Filled Gaps" data-zh="已填充Gap">Filled Gaps</span>
    </div>
    
    <div class="test-item">
        <span data-en="Fill Success Rate" data-zh="填充成功率">Fill Success Rate</span>
    </div>
    
    <div class="test-item">
        <span data-en="Fill Type Distribution" data-zh="填充类型分布">Fill Type Distribution</span>
    </div>
    
    <div class="test-item">
        <span data-en="Extension" data-zh="延申填充">Extension</span>
    </div>
    
    <div class="test-item">
        <span data-en="Direct Connection" data-zh="直接连接">Direct Connection</span>
    </div>
    
    <script>
        let currentLanguage = 'en';
        
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // Update language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent === (lang === 'zh' ? '中文' : 'English')) {
                    btn.classList.add('active');
                }
            });
            
            // Update all translatable elements
            document.querySelectorAll('[data-en][data-zh]').forEach(element => {
                if (lang === 'zh') {
                    element.textContent = element.getAttribute('data-zh');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }
    </script>
</body>
</html>

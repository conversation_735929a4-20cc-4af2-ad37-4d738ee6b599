<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body { font-family: 'Segoe UI', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; font-weight: 300; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; background: #f8f9fa; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #667eea; }
        .stat-label { color: #666; margin-top: 5px; }
        .chromosome-section { padding: 30px; }
        .chromosome-title { font-size: 1.5em; font-weight: bold; margin-bottom: 20px; color: #333; }
        .gap-track { position: relative; height: 60px; background: #e9ecef; border-radius: 5px; margin-bottom: 20px; }
        .gap-item { position: absolute; height: 100%; border-radius: 3px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold; }
        .gap-filled { background: #28a745; }
        .gap-unfilled { background: #dc3545; }
        .legend { display: flex; gap: 20px; margin-bottom: 20px; }
        .legend-item { display: flex; align-items: center; gap: 8px; }
        .legend-color { width: 20px; height: 20px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DEGAP Gap Filling Report</h1>
            <p>Genome gap filling results visualization</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">20</div>
                <div class="stat-label">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">60.0%</div>
                <div class="stat-label">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1</div>
                <div class="stat-label">Chromosomes</div>
            </div>
        </div>
        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Fill Type Distribution</h3>
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">Unknown</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">100.0%</div>
            </div>
        </div>
        <div class="chromosome-section">
            <div class="chromosome-title">chr1A_TA299</div>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span>Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span>Unfilled Gap</span>
                </div>
            </div>
            <div class="gap-track">
                <div class="gap-item gap-unfilled" 
                     style="left: 0.02%; width: 0.00%;"
                     title="Gap 1: 138,402-138,424 (23 bp)">
                    1
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 0.04%; width: 0.00%;"
                     title="Gap 2: 254,354-272,628 (18,275 bp)">
                    2
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 4.73%; width: 0.00%;"
                     title="Gap 3: 30,054,223-30,054,322 (100 bp)">
                    3
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 6.75%; width: 0.01%;"
                     title="Gap 4: 42,926,970-42,976,731 (49,762 bp)">
                    4
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 9.07%; width: 0.00%;"
                     title="Gap 5: 57,693,457-57,694,440 (984 bp)">
                    5
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 11.24%; width: 0.00%;"
                     title="Gap 6: 71,479,355-71,482,452 (3,098 bp)">
                    6
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 17.30%; width: 0.00%;"
                     title="Gap 7: 110,023,940-110,027,276 (3,337 bp)">
                    7
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 23.64%; width: 0.00%;"
                     title="Gap 8: 150,338,264-150,343,236 (4,973 bp)">
                    8
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 25.11%; width: 0.00%;"
                     title="Gap 9: 159,684,345-159,685,915 (1,571 bp)">
                    9
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 28.66%; width: 0.00%;"
                     title="Gap 10: 182,271,447-182,271,469 (23 bp)">
                    10
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 32.70%; width: 0.00%;"
                     title="Gap 11: 207,927,380-207,929,943 (2,564 bp)">
                    11
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 45.01%; width: 0.00%;"
                     title="Gap 12: 286,215,887-286,218,916 (3,030 bp)">
                    12
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 48.02%; width: 0.00%;"
                     title="Gap 13: 305,356,130-305,357,762 (1,633 bp)">
                    13
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 67.93%; width: 0.00%;"
                     title="Gap 14: 431,979,574-431,982,803 (3,230 bp)">
                    14
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 95.40%; width: 0.00%;"
                     title="Gap 15: 606,694,072-606,694,094 (23 bp)">
                    15
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 96.45%; width: 0.00%;"
                     title="Gap 16: 613,338,462-613,342,080 (3,619 bp)">
                    16
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 97.02%; width: 0.00%;"
                     title="Gap 17: 617,024,912-617,025,585 (674 bp)">
                    17
                </div>
                <div class="gap-item gap-unfilled" 
                     style="left: 97.15%; width: 0.00%;"
                     title="Gap 18: 617,827,052-617,830,319 (3,268 bp)">
                    18
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 99.52%; width: 0.00%;"
                     title="Gap 19: 632,889,350-632,890,236 (887 bp)">
                    19
                </div>
                <div class="gap-item gap-filled" 
                     style="left: 100.00%; width: 0.00%;"
                     title="Gap 20: 635,944,767-635,944,789 (23 bp)">
                    20
                </div>
            </div>
        </div>
    </div>
</body>
</html>
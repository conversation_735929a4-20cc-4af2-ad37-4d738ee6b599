#!/usr/bin/env python3
"""
DEGAP HTML Report Generator
Generate interactive HTML visualization for DEGAP gap filling results
"""

import os
import sys
import json
import argparse
from collections import defaultdict
import re

def parse_gap_log(gap_log_path):
    """Parse gap.log file to get original gap information"""
    gaps = {}
    if not os.path.exists(gap_log_path):
        return gaps
    
    with open(gap_log_path, 'r') as f:
        next(f)  # Skip header
        for line in f:
            parts = line.strip().split('\t')
            if len(parts) >= 7:
                chrom = parts[0]
                gap_id = int(parts[1])
                start = int(parts[2])
                end = int(parts[3])
                gap_len = int(parts[4])
                left_len = int(parts[5])
                right_len = int(parts[6])
                
                if chrom not in gaps:
                    gaps[chrom] = {}
                
                gaps[chrom][gap_id] = {
                    'start': start,
                    'end': end,
                    'gap_len': gap_len,
                    'left_len': left_len,
                    'right_len': right_len,
                    'status': 'unfilled',
                    'fill_direction': None,
                    'fill_length': None,
                    'fill_type': None,
                    'new_start': start,
                    'new_end': end
                }
    return gaps

def parse_detailed_process_log(detailed_log_path):
    """Parse detailed_process.log to get filling results"""
    filled_info = {}
    unfilled_info = {}
    
    if not os.path.exists(detailed_log_path):
        return filled_info, unfilled_info
    
    with open(detailed_log_path, 'r') as f:
        content = f.read()
    
    # Parse filled gaps
    filled_section = False
    unfilled_section = False
    
    for line in content.split('\n'):
        line = line.strip()
        
        if line.startswith('## Filled Gap Details'):
            filled_section = True
            unfilled_section = False
            continue
        elif line.startswith('## Unfilled Gap Information'):
            filled_section = False
            unfilled_section = True
            continue
        elif line.startswith('## '):
            filled_section = False
            unfilled_section = False
            continue
        
        if filled_section and line.startswith('|') and not line.startswith('|--'):
            parts = [p.strip() for p in line.split('|')[1:-1]]  # Remove empty first and last
            if len(parts) >= 7 and parts[0].isdigit():
                chrom = parts[1]
                gap_id = int(parts[2])
                original_pos = parts[3]
                original_len = int(parts[4])
                fill_direction = parts[5]
                fill_length = int(parts[6])
                new_pos = parts[7]
                
                if chrom not in filled_info:
                    filled_info[chrom] = {}
                
                filled_info[chrom][gap_id] = {
                    'original_pos': original_pos,
                    'original_len': original_len,
                    'fill_direction': fill_direction,
                    'fill_length': fill_length,
                    'new_pos': new_pos
                }
        
        elif unfilled_section and line.startswith('|') and not line.startswith('|--'):
            parts = [p.strip() for p in line.split('|')[1:-1]]
            if len(parts) >= 5 and parts[0].isdigit():
                chrom = parts[1]
                gap_id = int(parts[2])
                position = parts[3]
                length = int(parts[4])
                reason = parts[5] if len(parts) > 5 else 'Unknown'
                
                if chrom not in unfilled_info:
                    unfilled_info[chrom] = {}
                
                unfilled_info[chrom][gap_id] = {
                    'position': position,
                    'length': length,
                    'reason': reason
                }
    
    return filled_info, unfilled_info

def analyze_fill_types(output_dir, gaps):
    """Analyze fill types by reading process.log files from DEGAP2.0_Output"""
    degap_output_dir = os.path.join(output_dir, 'DEGAP2.0_Output')
    if not os.path.exists(degap_output_dir):
        return gaps
    
    for chrom in gaps:
        for gap_id in gaps[chrom]:
            gap_name = f"{chrom}.{gap_id}"
            
            # Check both directions
            for direction in ['left', 'right']:
                result_dir = os.path.join(degap_output_dir, f"{gap_name}.{direction}")
                process_log = os.path.join(result_dir, 'process.log')
                
                if os.path.exists(process_log):
                    with open(process_log, 'r') as f:
                        content = f.read()
                    
                    if "GAP can be closed!" in content:
                        gaps[chrom][gap_id]['status'] = 'filled'
                        gaps[chrom][gap_id]['fill_direction'] = direction

                        # Determine fill type
                        if "Direct overlap" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Direct Connection'
                        elif "Tandem repeat" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Tandem Repeat'
                        elif "Endloop!" in content:
                            gaps[chrom][gap_id]['fill_type'] = 'Extension'
                        else:
                            gaps[chrom][gap_id]['fill_type'] = 'Unknown'

                        # Extract gap length and other details
                        for line in content.split('\n'):
                            if "GAP Length:" in line:
                                try:
                                    gap_length = int(line.split(':')[1].strip())
                                    gaps[chrom][gap_id]['fill_length'] = gap_length
                                except:
                                    pass
                            elif "extensionSequneceMinimumExtensionLength:" in line:
                                try:
                                    ext_len = int(line.split(':')[1].strip())
                                    gaps[chrom][gap_id]['extension_length'] = ext_len
                                except:
                                    pass
                        break
    
    return gaps

def analyze_gap_length_distribution(gaps):
    """Analyze gap length distribution"""
    length_ranges = {
        'Very Small (< 100 bp)': 0,
        'Small (100-1K bp)': 0,
        'Medium (1K-10K bp)': 0,
        'Large (10K-50K bp)': 0,
        'Very Large (> 50K bp)': 0
    }

    filled_length_ranges = {
        'Very Small (< 100 bp)': 0,
        'Small (100-1K bp)': 0,
        'Medium (1K-10K bp)': 0,
        'Large (10K-50K bp)': 0,
        'Very Large (> 50K bp)': 0
    }

    for chrom in gaps:
        for gap_id in gaps[chrom]:
            gap = gaps[chrom][gap_id]
            gap_len = gap['gap_len']

            # Categorize by length
            if gap_len < 100:
                length_ranges['Very Small (< 100 bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Very Small (< 100 bp)'] += 1
            elif gap_len < 1000:
                length_ranges['Small (100-1K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Small (100-1K bp)'] += 1
            elif gap_len < 10000:
                length_ranges['Medium (1K-10K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Medium (1K-10K bp)'] += 1
            elif gap_len < 50000:
                length_ranges['Large (10K-50K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Large (10K-50K bp)'] += 1
            else:
                length_ranges['Very Large (> 50K bp)'] += 1
                if gap['status'] == 'filled':
                    filled_length_ranges['Very Large (> 50K bp)'] += 1

    return length_ranges, filled_length_ranges

def generate_html_report(gaps, output_path):
    """Generate interactive HTML report"""

    # Calculate statistics
    total_gaps = sum(len(gaps[chrom]) for chrom in gaps)
    filled_gaps = sum(1 for chrom in gaps for gap_id in gaps[chrom] if gaps[chrom][gap_id]['status'] == 'filled')
    fill_rate = (filled_gaps / total_gaps * 100) if total_gaps > 0 else 0

    # Group by fill type
    fill_types = defaultdict(int)
    for chrom in gaps:
        for gap_id in gaps[chrom]:
            if gaps[chrom][gap_id]['status'] == 'filled':
                fill_type = gaps[chrom][gap_id]['fill_type'] or 'Unknown'
                fill_types[fill_type] += 1

    # Analyze gap length distribution
    length_ranges, filled_length_ranges = analyze_gap_length_distribution(gaps)
    
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DEGAP Gap Filling Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }}
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }}
        .stat-label {{
            color: #666;
            margin-top: 5px;
        }}
        .controls {{
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }}
        .chromosome-selector {{
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }}
        .chromosome-btn {{
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }}
        .chromosome-btn.active {{
            background: #667eea;
            color: white;
        }}
        .chromosome-btn:hover {{
            background: #667eea;
            color: white;
        }}
        .gap-visualization {{
            padding: 30px;
        }}
        .chromosome-section {{
            margin-bottom: 40px;
            display: none;
        }}
        .chromosome-section.active {{
            display: block;
        }}
        .chromosome-title {{
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }}
        .gap-track {{
            position: relative;
            height: 60px;
            background: #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            overflow: hidden;
        }}
        .gap-item {{
            position: absolute;
            height: 100%;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }}
        .gap-item:hover {{
            transform: scaleY(1.2);
            z-index: 10;
        }}
        .gap-filled {{
            background: #28a745;
        }}
        .gap-unfilled {{
            background: #dc3545;
        }}
        .gap-details {{
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }}
        .gap-details.show {{
            display: block;
        }}
        .detail-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        .detail-item {{
            background: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }}
        .detail-label {{
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }}
        .detail-value {{
            color: #666;
        }}
        .legend {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DEGAP Gap Filling Report</h1>
            <p>Interactive visualization of genome gap filling results</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{total_gaps}</div>
                <div class="stat-label">Total Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{filled_gaps}</div>
                <div class="stat-label">Filled Gaps</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{fill_rate:.1f}%</div>
                <div class="stat-label">Fill Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{len(gaps)}</div>
                <div class="stat-label">Chromosomes</div>
            </div>
        </div>

        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Fill Type Distribution</h3>"""

    # Add fill type statistics
    for fill_type, count in fill_types.items():
        percentage = (count / filled_gaps * 100) if filled_gaps > 0 else 0
        html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{count}</div>
                <div class="stat-label">{fill_type}</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">{percentage:.1f}%</div>
            </div>"""

    html_content += """
        </div>

        <div class="stats">
            <h3 style="grid-column: 1/-1; margin: 0 0 20px 0; color: #333;">Gap Length Distribution</h3>"""

    # Add length distribution statistics
    for length_range, total_count in length_ranges.items():
        filled_count = filled_length_ranges[length_range]
        success_rate = (filled_count / total_count * 100) if total_count > 0 else 0
        html_content += f"""
            <div class="stat-card">
                <div class="stat-number">{filled_count}/{total_count}</div>
                <div class="stat-label">{length_range}</div>
                <div style="font-size: 0.9em; color: #888; margin-top: 5px;">Success: {success_rate:.1f}%</div>
            </div>"""

    html_content += """
        </div>

        <div class="controls">
            <h3>Select Chromosome:</h3>
            <div class="chromosome-selector">"""

    # Add chromosome buttons
    for chrom in sorted(gaps.keys()):
        html_content += f"""
                <button class="chromosome-btn" onclick="showChromosome('{chrom}')">{chrom}</button>"""

    html_content += """
            </div>
        </div>

        <div class="gap-visualization">
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color gap-filled"></div>
                    <span>Filled Gap</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color gap-unfilled"></div>
                    <span>Unfilled Gap</span>
                </div>
            </div>"""

    # Add chromosome sections
    for chrom in sorted(gaps.keys()):
        chrom_gaps = gaps[chrom]
        if not chrom_gaps:
            continue

        # Calculate chromosome length (approximate from gap positions)
        max_pos = max(gap['end'] for gap in chrom_gaps.values())

        html_content += f"""
            <div class="chromosome-section" id="chr-{chrom}">
                <div class="chromosome-title">{chrom}</div>
                <div class="gap-track" id="track-{chrom}">"""

        # Add gap items
        for gap_id in sorted(chrom_gaps.keys()):
            gap = chrom_gaps[gap_id]
            left_percent = (gap['start'] / max_pos) * 100
            width_percent = ((gap['end'] - gap['start']) / max_pos) * 100

            status_class = 'gap-filled' if gap['status'] == 'filled' else 'gap-unfilled'

            html_content += f"""
                    <div class="gap-item {status_class}"
                         style="left: {left_percent:.2f}%; width: {width_percent:.2f}%;"
                         onclick="showGapDetails('{chrom}', {gap_id})"
                         title="Gap {gap_id}: {gap['start']:,}-{gap['end']:,} ({gap['gap_len']:,} bp)">
                        {gap_id}
                    </div>"""

        html_content += """
                </div>
                <div class="gap-details" id="details-""" + chrom + """">
                    <div class="detail-grid" id="detail-grid-""" + chrom + """">
                        <!-- Gap details will be populated by JavaScript -->
                    </div>
                </div>
            </div>"""

    # Add JavaScript and closing tags
    html_content += """
        </div>
    </div>

    <script>
        // Gap data for JavaScript
        const gapData = """ + json.dumps(gaps, indent=8) + """;

        function showChromosome(chromName) {
            // Hide all chromosome sections
            const sections = document.querySelectorAll('.chromosome-section');
            sections.forEach(section => section.classList.remove('active'));

            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.chromosome-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Show selected chromosome
            const selectedSection = document.getElementById('chr-' + chromName);
            if (selectedSection) {
                selectedSection.classList.add('active');
            }

            // Activate selected button
            event.target.classList.add('active');

            // Hide gap details
            const details = document.getElementById('details-' + chromName);
            if (details) {
                details.classList.remove('show');
            }
        }

        function showGapDetails(chromName, gapId) {
            const gap = gapData[chromName][gapId];
            const detailsDiv = document.getElementById('details-' + chromName);
            const gridDiv = document.getElementById('detail-grid-' + chromName);

            if (!gap || !detailsDiv || !gridDiv) return;

            // Build details HTML
            let detailsHTML = `
                <div class="detail-item">
                    <div class="detail-label">Gap ID</div>
                    <div class="detail-value">${gapId}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Position</div>
                    <div class="detail-value">${gap.start.toLocaleString()} - ${gap.end.toLocaleString()}</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Original Length</div>
                    <div class="detail-value">${gap.gap_len.toLocaleString()} bp</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Status</div>
                    <div class="detail-value" style="color: ${gap.status === 'filled' ? '#28a745' : '#dc3545'}">
                        ${gap.status === 'filled' ? 'Filled' : 'Unfilled'}
                    </div>
                </div>`;

            if (gap.status === 'filled') {
                detailsHTML += `
                    <div class="detail-item">
                        <div class="detail-label">Fill Direction</div>
                        <div class="detail-value">${gap.fill_direction || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Fill Type</div>
                        <div class="detail-value">${gap.fill_type || 'Unknown'}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Fill Length</div>
                        <div class="detail-value">${gap.fill_length !== null ? gap.fill_length.toLocaleString() + ' bp' : 'Unknown'}</div>
                    </div>`;
            }

            detailsHTML += `
                <div class="detail-item">
                    <div class="detail-label">Left Flanking Length</div>
                    <div class="detail-value">${gap.left_len.toLocaleString()} bp</div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">Right Flanking Length</div>
                    <div class="detail-value">${gap.right_len.toLocaleString()} bp</div>
                </div>`;

            gridDiv.innerHTML = detailsHTML;
            detailsDiv.classList.add('show');
        }

        // Show first chromosome by default
        document.addEventListener('DOMContentLoaded', function() {
            const firstBtn = document.querySelector('.chromosome-btn');
            if (firstBtn) {
                firstBtn.click();
            }
        });
    </script>
</body>
</html>"""

    with open(output_path, 'w') as f:
        f.write(html_content)

def main():
    parser = argparse.ArgumentParser(description='Generate DEGAP HTML report')
    parser.add_argument('--output-dir', '-o', default='.', 
                       help='DEGAP output directory (default: current directory)')
    parser.add_argument('--html-output', default='degap_report.html',
                       help='Output HTML file name (default: degap_report.html)')
    
    args = parser.parse_args()
    
    output_dir = os.path.abspath(args.output_dir)
    
    # Check if required files exist
    gap_log = os.path.join(output_dir, 'gap.log')
    detailed_log = os.path.join(output_dir, 'detailed_process.log')
    
    if not os.path.exists(gap_log):
        print(f"Error: Cannot find gap.log in {output_dir}")
        sys.exit(1)
    
    print("Parsing gap information...")
    gaps = parse_gap_log(gap_log)
    
    print("Parsing detailed process log...")
    filled_info, unfilled_info = parse_detailed_process_log(detailed_log)
    
    print("Analyzing fill types...")
    gaps = analyze_fill_types(output_dir, gaps)
    
    # Update gaps with detailed information
    for chrom in filled_info:
        if chrom in gaps:
            for gap_id in filled_info[chrom]:
                if gap_id in gaps[chrom]:
                    info = filled_info[chrom][gap_id]
                    gaps[chrom][gap_id]['status'] = 'filled'
                    gaps[chrom][gap_id]['fill_direction'] = info['fill_direction']
                    gaps[chrom][gap_id]['fill_length'] = info['fill_length']
    
    html_output = os.path.join(output_dir, args.html_output)
    print(f"Generating HTML report: {html_output}")
    generate_html_report(gaps, html_output)
    
    print(f"HTML report generated successfully: {html_output}")

if __name__ == '__main__':
    main()

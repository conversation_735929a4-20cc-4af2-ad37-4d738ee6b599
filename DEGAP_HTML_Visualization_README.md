# DEGAP HTML可视化报告生成器

## 概述

`generate_degap_html_report.py` 是一个用于生成DEGAP gap填充结果的交互式HTML可视化报告的脚本。该脚本可以读取DEGAP的输出日志文件，生成一个美观的、交互式的HTML报告，展示gap填充的详细情况。

## 功能特性

### 1. 综合统计信息
- **总gap数量**：显示检测到的总gap数
- **已填充gap数量**：显示成功填充的gap数
- **填充成功率**：计算并显示填充成功的百分比
- **染色体数量**：显示涉及的染色体数量

### 2. 填充类型分布
- **Extension（延申填充）**：通过序列延申填充gap
- **Direct Connection（直接连接）**：左右序列直接重叠连接
- **Tandem Repeat（串联重复）**：通过串联重复序列填充
- **Unknown（未知类型）**：其他类型的填充

### 3. Gap长度分布分析
- **Very Small (< 100 bp)**：非常小的gap
- **Small (100-1K bp)**：小gap
- **Medium (1K-10K bp)**：中等gap
- **Large (10K-50K bp)**：大gap
- **Very Large (> 50K bp)**：非常大的gap

每个长度范围显示总数量和填充成功率。

### 4. 交互式染色体可视化
- **染色体选择器**：点击按钮切换不同染色体
- **Gap可视化轨道**：以比例显示gap在染色体上的位置
- **颜色编码**：
  - 🟢 绿色：已填充的gap
  - 🔴 红色：未填充的gap
- **详细信息面板**：点击gap显示详细信息

### 5. 详细Gap信息
点击任意gap可查看：
- Gap ID和位置信息
- 原始长度和填充状态
- 填充方向（left/right）
- 填充类型和填充长度
- 左右侧翼序列长度

## 使用方法

### 基本用法

```bash
# 在DEGAP输出目录中运行
python generate_degap_html_report.py --output-dir . --html-output degap_report.html
```

### 参数说明

- `--output-dir` / `-o`：DEGAP输出目录路径（默认：当前目录）
- `--html-output`：输出HTML文件名（默认：degap_report.html）

### 示例

```bash
# 使用默认设置
python generate_degap_html_report.py

# 指定输出目录和文件名
python generate_degap_html_report.py --output-dir /path/to/degap/output --html-output my_report.html

# 在DEGAP输出目录中运行（推荐）
cd /path/to/degap/output
python /path/to/generate_degap_html_report.py --output-dir . --html-output degap_visualization.html
```

## 输入文件要求

脚本需要以下文件存在于指定的输出目录中：

### 必需文件
- `gap.log`：原始gap信息文件
- `detailed_process.log`：详细处理日志文件

### 可选文件
- `DEGAP2.0_Output/`：包含各个gap的详细结果目录
  - `{chromosome}.{gap_id}.{direction}/process.log`：各个gap的处理日志

## 输出文件

生成的HTML文件是一个完全自包含的文件，包含：
- 所有必要的CSS样式
- JavaScript交互功能
- 完整的gap数据（JSON格式嵌入）

可以直接在任何现代浏览器中打开，无需额外依赖。

## 技术特性

- **响应式设计**：适配不同屏幕尺寸
- **现代UI**：使用渐变色和阴影效果
- **交互式**：点击切换和悬停效果
- **数据驱动**：基于实际DEGAP输出数据
- **自包含**：单个HTML文件包含所有功能

## 故障排除

### 常见问题

1. **找不到gap.log文件**
   ```
   Error: Cannot find gap.log in /path/to/output
   ```
   - 确保在正确的DEGAP输出目录中运行
   - 检查DEGAP是否成功完成初始化阶段

2. **没有填充类型信息**
   - 确保`DEGAP2.0_Output`目录存在
   - 检查各个gap的`process.log`文件是否存在

3. **数据显示不完整**
   - 确保`detailed_process.log`文件存在且完整
   - 检查DEGAP是否完成了post-processing阶段

### 调试模式

可以通过修改脚本添加调试信息：

```python
# 在脚本开头添加
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 集成到AutoGapfiller

该脚本可以集成到AutoGapfiller的post-processing阶段，自动生成可视化报告。

## 浏览器兼容性

- Chrome/Chromium 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

与DEGAP项目使用相同的许可证。
